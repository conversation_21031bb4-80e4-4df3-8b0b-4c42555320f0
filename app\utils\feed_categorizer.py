# feed_categorizer.py
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
import json
import asyncio
import copy
import re
from typing import List, Dict, Any, Optional

# --- Constants ---
# Threshold for keyword matches in the strict fallback filter.
FALLBACK_RELEVANCE_SCORE_THRESHOLD = 3

# --- Helper Functions ---
def _clean_comment_content(comment: str) -> str:
    """
    Clean comment content by removing hashtags and normalizing whitespace.

    Args:
        comment: The original comment text

    Returns:
        Cleaned comment text without hashtags
    """
    if not comment:
        return comment

    # Remove hashtags - comprehensive patterns:
    # 1. #word (basic hashtag)
    # 2. #WordWithCaps (camelCase hashtags)
    # 3. #word_with_underscores
    # 4. Multiple hashtags in sequence
    comment = re.sub(r'#\w+(?:_\w+)*', '', comment)

    # Remove any remaining # symbols that might be standalone
    comment = re.sub(r'#', '', comment)

    # Remove extra whitespace and normalize
    comment = ' '.join(comment.split())

    # Remove trailing/leading whitespace
    comment = comment.strip()

    return comment

# --- Local Class Definition ---
# Local definition to avoid circular import issues.
class FeedPostItem:
    """Local definition of FeedPostItem to avoid circular import."""
    def __init__(self, activity_urn: str, text: str, total_reactions: int = 0,
                 total_comments: int = 0, total_shares: int = 0, author_urn: str = ""):
        self.activity_urn = activity_urn
        self.text = text
        self.total_reactions = total_reactions
        self.total_comments = total_comments
        self.total_shares = total_shares
        self.author_urn = author_urn

# --- Helper Functions ---
def _extract_json_from_response(response_text: str) -> str:
    """
    Extracts a JSON string from a model's text response that might be wrapped in markdown.
    """
    if "```json" in response_text:
        start = response_text.find("```json") + 7
        end = response_text.rfind("```")
        if end > start:
            return response_text[start:end].strip()
    elif "```" in response_text:
        start = response_text.find("```") + 3
        end = response_text.rfind("```")
        if end > start:
            return response_text[start:end].strip()
    return response_text

# --- Core Categorization Pipeline ---
async def categorize_feed_posts(
    posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Categorizes posts into persona-specific hierarchical categories, filters irrelevant posts,
    and generates engagement recommendations concurrently.

    IMPORTANT: This function generates fresh, dynamic categories on every request.
    Categories are NOT cached or reused - each API call produces unique categorization
    based on the provided persona keywords and post content.
    """
    print("\n=== FEED CATEGORIZATION START ===")
    print(f"Input posts: {len(posts)}")
    
    if not posts:
        print("No posts provided. Returning empty result.")
        return []

    relevant_posts = await _filter_posts_by_persona_relevance_strict(
        posts, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )
    print(f"Relevant posts after filtering: {len(relevant_posts)}")

    if not relevant_posts:
        print("No posts found relevant to the persona. Returning empty result.")
        return []

    persona_categories = await _generate_persona_categories_from_posts(
        relevant_posts, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )
    if not persona_categories:
        print("No categories could be generated. Returning empty result.")
        return []

    categorized_feed = await _categorize_posts_into_persona_categories(
        relevant_posts, persona_categories, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )

    categorized_feed = _validate_and_clean_categorized_feed(categorized_feed, relevant_posts)
    categorized_feed = _remove_empty_categories(categorized_feed)

    # IMPROVEMENT: Process all sub-categories concurrently instead of in a sequential loop.
    processing_tasks = []
    for category in categorized_feed:
        for subcat in category.get("sub_categories", []):
            task = _process_subcategory_concurrently(
                subcat, category.get("category_name", ""), general_persona_keywords,
                content_persona_keywords, network_persona_keywords
            )
            processing_tasks.append(task)
    
    await asyncio.gather(*processing_tasks)

    # Final filtering of any sub-categories that might be empty after processing
    categorized_feed = _remove_empty_categories(categorized_feed)
    
    total_final_posts = sum(len(subcat.get("posts", [])) for cat in categorized_feed for subcat in cat.get("sub_categories", []))
    
    print(f"Final output posts: {total_final_posts}")
    print("=== FEED CATEGORIZATION COMPLETE ===")
    
    return categorized_feed if total_final_posts > 0 else []

async def _process_subcategory_concurrently(
    subcat: Dict[str, Any],
    category_name: str,
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
):
    """
    Processes a single sub-category: sorts its posts, and concurrently generates
    its summary and recommendations for all its posts. Modifies the subcat dict in-place.
    """
    subcat_posts = subcat.get("posts", [])
    if not subcat_posts:
        return

    # 1. Sort posts by engagement (ensure all posts are dictionaries)
    subcat_posts = [p for p in subcat_posts if isinstance(p, dict)]
    subcat_posts.sort(
        key=lambda p: p.get("total_reactions", 0) + p.get("total_comments", 0) + p.get("total_shares", 0),
        reverse=True
    )

    # 2. Create concurrent tasks for summary and all post recommendations
    summary_task = _generate_persona_category_summary(
        subcat.get("sub_categories_name", ""), subcat_posts, general_persona_keywords,
        content_persona_keywords, network_persona_keywords
    )
    
    recommendation_tasks = [
        _generate_persona_post_recommendations(
            post, category_name, subcat.get("sub_categories_name", ""),
            general_persona_keywords, content_persona_keywords, network_persona_keywords
        )
        for post in subcat_posts
    ]

    # 3. Execute all tasks for this sub-category in parallel
    results = await asyncio.gather(summary_task, *recommendation_tasks)

    # 4. Unpack results and update the sub-category dictionary
    subcat["summary"] = results[0]
    recommendations = results[1:]
    
    subcat["author_member_urns"] = sorted(list({post.get("author_urn") for post in subcat_posts if post.get("author_urn")}))

    recommended_posts = [
        {
            "activity_urn": post.get("activity_urn", "urn:li:activity:missing"),
            "recommended_reaction": rec["reaction"],
            "suggested_comment": rec["comment"]
        }
        for post, rec in zip(subcat_posts, recommendations)
    ]
    subcat["posts"] = recommended_posts

async def _filter_posts_by_persona_relevance_strict(
    posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
) -> List[FeedPostItem]:
    posts_data = [{"id": i, "text": post.text} for i, post in enumerate(posts)]
    prompt = TEMPLATES["strict_persona_post_filtering"].format(
        posts_data=json.dumps(posts_data, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords or []),
        network_persona_keywords=", ".join(network_persona_keywords or [])
    )
    try:
        # IMPROVEMENT: Run potentially blocking I/O in a separate thread to not block the event loop.
        response = await asyncio.to_thread(model.generate_content, prompt)
        response_text = _extract_json_from_response(response.text.strip())
        result = json.loads(response_text)
        relevant_post_ids = result.get("relevant_post_ids", [])
        relevant_posts = [posts[post_id] for post_id in relevant_post_ids if post_id < len(posts)]
        print(f"AI filtering selected {len(relevant_posts)} out of {len(posts)} posts as relevant.")
        return relevant_posts
    except (json.JSONDecodeError, ValueError) as e:
        print(f"AI strict post filtering failed: {e}. Falling back to keyword-based filtering.")
        return _fallback_strict_post_filtering(posts_data, general_persona_keywords, posts)

async def _generate_persona_categories_from_posts(
    relevant_posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
) -> List[Dict[str, Any]]:
    posts_data = [{"id": i, "text": post.text} for i, post in enumerate(relevant_posts)]

    # Add timestamp and random element to ensure fresh generation every time
    import time
    import random
    timestamp = int(time.time() * 1000)  # millisecond precision
    random_seed = random.randint(1000, 9999)

    prompt = TEMPLATES["persona_categories_from_posts"].format(
        posts_data=json.dumps(posts_data, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords or []),
        network_persona_keywords=", ".join(network_persona_keywords or [])
    )

    # Add unique identifier to prompt to ensure no caching
    prompt += f"\n\n[GENERATION_ID: {timestamp}_{random_seed}]"

    try:
        # Disable caching for category generation to ensure fresh results
        response = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
        response_text = _extract_json_from_response(response.text.strip())
        result = json.loads(response_text)
        print(f"Generated {len(result.get('categories', []))} fresh persona categories for request {timestamp}_{random_seed}")
        return result.get("categories", [])
    except (json.JSONDecodeError, ValueError) as e:
        print(f"AI persona categories generation failed: {e}. Falling back to keyword-based categories.")
        return _fallback_persona_categories_from_posts(posts_data, general_persona_keywords)

async def _categorize_posts_into_persona_categories(
    posts: List[FeedPostItem],
    persona_categories: List[Dict[str, Any]],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
) -> List[Dict[str, Any]]:
    posts_data = [{"id": i, "text": post.text} for i, post in enumerate(posts)]

    # Add timestamp and random element to ensure fresh categorization every time
    import time
    import random
    timestamp = int(time.time() * 1000)  # millisecond precision
    random_seed = random.randint(1000, 9999)

    prompt = TEMPLATES["persona_post_categorization"].format(
        posts_data=json.dumps(posts_data, indent=2),
        persona_categories=json.dumps(persona_categories, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords or []),
        network_persona_keywords=", ".join(network_persona_keywords or [])
    )

    # Add unique identifier to prompt to ensure no caching
    prompt += f"\n\n[CATEGORIZATION_ID: {timestamp}_{random_seed}]"

    try:
        # Disable caching for post categorization to ensure fresh results
        response = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
        response_text = _extract_json_from_response(response.text.strip())
        result = json.loads(response_text)
        categorized_result = result.get("categorized_posts", [])
        for category in categorized_result:
            for sub_cat in category.get("sub_categories", []):
                original_posts = []
                for ai_post in sub_cat.get("posts", []):
                    # Fix type checking order to prevent calling .get() on integers
                    if isinstance(ai_post, dict):
                        post_id = ai_post.get("id")
                    elif isinstance(ai_post, int):
                        post_id = ai_post
                    else:
                        post_id = None
                    if post_id is not None and post_id < len(posts):
                        original_post = posts[post_id]
                        original_posts.append(vars(original_post))
                sub_cat["posts"] = original_posts
        return categorized_result
    except (json.JSONDecodeError, ValueError) as e:
        print(f"AI post categorization failed: {e}. Falling back to keyword-based categorization.")

        # Convert posts to dictionaries with proper error handling
        posts_as_dicts = []
        for i, p in enumerate(posts):
            try:
                if hasattr(p, '__dict__'):
                    post_dict = vars(p)
                elif isinstance(p, dict):
                    post_dict = p
                else:
                    print(f"DEBUG: Unexpected post type at index {i}: {type(p)}, converting to dict")
                    post_dict = {"text": str(p), "activity_urn": f"unknown_{i}"}

                # Ensure the post_dict has required fields
                if not isinstance(post_dict, dict):
                    print(f"DEBUG: Failed to convert post at index {i} to dict, skipping")
                    continue

                posts_as_dicts.append(post_dict)
            except Exception as conversion_error:
                print(f"DEBUG: Error converting post at index {i}: {conversion_error}")
                continue

        print(f"DEBUG: Successfully converted {len(posts_as_dicts)} posts to dictionaries")
        return _fallback_persona_post_categorization(posts_as_dicts, persona_categories)

def _validate_and_clean_categorized_feed(
    categorized_feed: List[Dict[str, Any]],
    original_posts: List[FeedPostItem]
) -> List[Dict[str, Any]]:
    seen_activity_urns = set()
    original_activity_urns = {post.activity_urn for post in original_posts if post.activity_urn}
    cleaned_feed = []
    for category in categorized_feed:
        cleaned_sub_categories = []
        for sub_cat in category.get("sub_categories", []):
            unique_posts = []
            for post in sub_cat.get("posts", []):
                # Add type checking to handle cases where post might be an integer or other type
                if not isinstance(post, dict):
                    print(f"WARNING: Skipping non-dict post in validation: type={type(post)}, value={post}")
                    continue

                activity_urn = post.get("activity_urn")
                if activity_urn and activity_urn in original_activity_urns and activity_urn not in seen_activity_urns:
                    seen_activity_urns.add(activity_urn)
                    unique_posts.append(post)
            if unique_posts:
                sub_cat["posts"] = unique_posts
                cleaned_sub_categories.append(sub_cat)
        if cleaned_sub_categories:
            category["sub_categories"] = cleaned_sub_categories
            cleaned_feed.append(category)
    return cleaned_feed

def _remove_empty_categories(categorized_feed: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    non_empty_feed = []
    for category in categorized_feed:
        category["sub_categories"] = [
            subcat for subcat in category.get("sub_categories", []) if subcat.get("posts")
        ]
        if category.get("sub_categories"):
            non_empty_feed.append(category)
    return non_empty_feed

async def _generate_persona_category_summary(
    sub_category_name: str,
    posts: List[Dict[str, Any]],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
) -> str:
    sample_posts = [post.get("text", "") for post in posts[:5]]

    # Add timestamp and random element to ensure fresh summary generation every time
    import time
    import random
    timestamp = int(time.time() * 1000)  # millisecond precision
    random_seed = random.randint(1000, 9999)

    prompt = TEMPLATES["persona_category_summary"].format(
        category_name=sub_category_name,
        sample_posts="\n\n".join(sample_posts),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords or []),
        network_persona_keywords=", ".join(network_persona_keywords or [])
    )

    # Add unique identifier to prompt to ensure no caching
    prompt += f"\n\n[SUMMARY_ID: {timestamp}_{random_seed}]"

    try:
        # Disable caching for summary generation to ensure fresh results
        response = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
        return response.text.strip()
    except Exception as e:
        print(f"AI summary generation failed: {e}")
        return f"This category contains posts relevant to {sub_category_name.lower()}, tailored to your professional interests."

async def _generate_persona_post_recommendations(
    post: Dict[str, Any],
    category_name: str,
    sub_category_name: str,
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]],
    network_persona_keywords: Optional[List[str]]
) -> Dict[str, str]:
    # Add timestamp and random element to ensure fresh recommendations every time
    import time
    import random
    timestamp = int(time.time() * 1000)  # millisecond precision
    random_seed = random.randint(1000, 9999)

    prompt = TEMPLATES["persona_post_recommendations"].format(
        post_content=post.get("text", ""),
        author=post.get("author_urn", "Unknown"),
        category_name=category_name,
        sub_category_name=sub_category_name
    )

    # Add unique identifier to prompt to ensure no caching
    prompt += f"\n\n[RECOMMENDATION_ID: {timestamp}_{random_seed}]"

    # Retry logic for character length validation
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Disable caching for recommendations to ensure fresh results
            response = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
            response_text = _extract_json_from_response(response.text.strip())
            result = json.loads(response_text)

            comment = result.get("suggested_comment", "")
            reaction = result.get("recommended_reaction", "Like")

            # Post-processing: Remove hashtags and clean up
            comment = _clean_comment_content(comment)

            # Validate character length (150-200 characters)
            if 150 <= len(comment) <= 200:
                return {
                    "reaction": reaction,
                    "comment": comment
                }
            elif attempt < max_retries - 1:
                # Add length feedback to prompt for next attempt
                current_length = len(comment)
                if current_length < 150:
                    prompt += f"\n\nPREVIOUS ATTEMPT TOO SHORT ({current_length} chars). Make the comment longer to reach 150-200 characters."
                else:
                    prompt += f"\n\nPREVIOUS ATTEMPT TOO LONG ({current_length} chars). Make the comment shorter to stay within 150-200 characters."
                continue

        except (json.JSONDecodeError, ValueError) as e:
            print(f"AI persona recommendations attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                continue

    # QA-compliant fallback comment if all retries fail
    fallback_comment = f"Interesting insights here. This aligns with trends I've seen in {category_name.lower()}. How do you see this evolving in the industry?"

    # Ensure fallback meets character requirements
    if len(fallback_comment) > 200:
        fallback_comment = f"Great perspective on {category_name.lower()}. This matches what I've observed. What's your take on the next steps?"
    if len(fallback_comment) < 150:
        fallback_comment = f"Really valuable insights on {category_name.lower()}. This aligns perfectly with trends I've been seeing in the industry. How do you think this will evolve?"

    # Clean fallback comment (remove any potential hashtags)
    fallback_comment = _clean_comment_content(fallback_comment)

    return {"reaction": "Like", "comment": fallback_comment}

# --- Fallback Functions (Unchanged) ---
def _fallback_strict_post_filtering(
    posts_data: List[Dict[str, Any]],
    persona_keywords: List[str],
    original_posts: List[FeedPostItem]
) -> List[FeedPostItem]:
    relevant_posts = []
    keywords_lower = {kw.lower() for kw in persona_keywords}
    for i, post_data in enumerate(posts_data):
        content = post_data.get("text", "").lower()
        relevance_score = sum(1 for keyword in keywords_lower if keyword in content)
        if relevance_score >= FALLBACK_RELEVANCE_SCORE_THRESHOLD:
            relevant_posts.append(original_posts[i])
    print(f"Fallback filtering selected {len(relevant_posts)} out of {len(original_posts)} posts.")
    return relevant_posts

def _fallback_persona_categories_from_posts(
    posts_data: List[Dict[str, Any]],
    general_persona_keywords: List[str]
) -> List[Dict[str, Any]]:
    if not general_persona_keywords:
        return [{"category_name": "General Insights", "sub_categories": [{"sub_categories_name": "Relevant Topics", "posts": []}]}]
    categories = [
        {
            "category_name": keyword.replace("_", " ").title(),
            "sub_categories": [{"sub_categories_name": f"Discussions on {keyword.replace('_', ' ').title()}", "posts": []}]
        }
        for keyword in general_persona_keywords
    ]
    categories.append({"category_name": "Other Relevant Topics", "sub_categories": [{"sub_categories_name": "General Discussion", "posts": []}]})
    return categories

def _fallback_persona_post_categorization(
    posts_data: List[Dict[str, Any]],
    persona_categories: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    # Validate input types
    if not isinstance(posts_data, list) or not isinstance(persona_categories, list):
        print(f"ERROR: Invalid input types - posts_data: {type(posts_data)}, persona_categories: {type(persona_categories)}")
        return []

    # Validate persona_categories structure
    if not isinstance(persona_categories, list):
        print(f"ERROR: persona_categories is not a list: {type(persona_categories)}")
        return []

    # Filter out any non-dict items from persona_categories
    valid_categories = []
    for i, cat in enumerate(persona_categories):
        if isinstance(cat, dict):
            valid_categories.append(cat)
        else:
            print(f"DEBUG: Skipping non-dict category at index {i}: type={type(cat)}, value={cat}")

    if not valid_categories:
        print("ERROR: No valid categories found after filtering")
        return []

    result = copy.deepcopy(valid_categories)

    for i, post in enumerate(posts_data):
        # Add type checking to prevent calling .get() on non-dict objects
        if not isinstance(post, dict):
            print(f"DEBUG: Skipping non-dict post at index {i}: type={type(post)}, value={post}")
            continue

        content = post.get("text", "").lower()
        assigned = False
        for category in result[:-1]:
            # Add type checking for category as well
            if not isinstance(category, dict):
                print(f"DEBUG: Skipping non-dict category: type={type(category)}, value={category}")
                continue

            category_name = category.get("category_name", "")
            if not category_name:
                continue

            category_keywords = category_name.lower().split()
            if any(keyword in content for keyword in category_keywords):
                sub_categories = category.get("sub_categories")
                if sub_categories and isinstance(sub_categories, list) and len(sub_categories) > 0:
                    if isinstance(sub_categories[0], dict) and "posts" in sub_categories[0]:
                        sub_categories[0]["posts"].append(post)
                        assigned = True
                        break

        if not assigned and result and len(result) > 0:
            last_category = result[-1]
            if isinstance(last_category, dict):
                sub_categories = last_category.get("sub_categories")
                if sub_categories and isinstance(sub_categories, list) and len(sub_categories) > 0:
                    if isinstance(sub_categories[0], dict) and "posts" in sub_categories[0]:
                        sub_categories[0]["posts"].append(post)

    return result

# --- Legacy Function Stubs (for backward compatibility) ---
async def _categorize_posts_hierarchical(*args, **kwargs):
    return await categorize_feed_posts(*args, **kwargs)

async def _generate_category_summary(*args, **kwargs):
    return await _generate_persona_category_summary(*args, **kwargs)

async def _generate_post_recommendations(*args, **kwargs):
    return await _generate_persona_post_recommendations(*args, **kwargs)