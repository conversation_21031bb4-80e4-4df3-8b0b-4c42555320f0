"""
Comment Suggester Utility

This module provides functionality for generating LinkedIn comment suggestions
that meet QA standards including character length, context-aware endings,
genuine content alignment, and natural human-like tone.
"""

import json
import random
import re
from typing import List, Optional, Dict, Any
from app.utils.model_initializer import model


def _clean_comment_content(comment: str) -> str:
    """
    Clean comment content by removing hashtags and normalizing whitespace.

    Args:
        comment: The original comment text

    Returns:
        Cleaned comment text without hashtags
    """
    if not comment:
        return comment

    # Remove hashtags - comprehensive patterns:
    # 1. #word (basic hashtag)
    # 2. #WordWithCaps (camelCase hashtags)
    # 3. #word_with_underscores
    # 4. Multiple hashtags in sequence
    comment = re.sub(r'#\w+(?:_\w+)*', '', comment)

    # Remove any remaining # symbols that might be standalone
    comment = re.sub(r'#', '', comment)

    # Remove extra whitespace and normalize
    comment = ' '.join(comment.split())

    # Remove trailing/leading whitespace
    comment = comment.strip()

    return comment


def generate_comment_suggestion(
    post_content: str,
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> Dict[str, str]:
    """
    Generate a comment suggestion for a LinkedIn post based on persona keywords and post content.
    
    This function generates comments that meet strict QA standards:
    - 150-200 characters (including spaces and punctuation)
    - 1-3 sentences maximum
    - Context-aware endings (statements, questions, or calls-to-action)
    - Genuine content alignment (specific to post content, not generic templates)
    - Natural human-like tone (simple vocabulary, contractions, conversational language)
    
    Args:
        post_content (str): The LinkedIn post content to comment on
        general_persona_keywords (List[str]): General persona keywords defining the commenter's identity
        content_persona_keywords (Optional[List[str]]): Content-specific persona keywords (optional)
        network_persona_keywords (Optional[List[str]]): Network-specific persona keywords (optional)
    
    Returns:
        Dict[str, str]: Dictionary containing the suggested comment with key "comment"
    
    Raises:
        Exception: If comment generation fails after all retry attempts
    """
    # Set default values for optional parameters
    content_persona_keywords = content_persona_keywords or []
    network_persona_keywords = network_persona_keywords or []
    
    # Create content-focused prompt without persona dependency
    prompt = (
        "You are a professional LinkedIn engagement strategist specializing in content-based comment generation. "
        "Generate a comment that meets strict QA standards for Today's Feed functionality.\n\n"
        f"--- Post Content ---\n{post_content}\n--- End Post Content ---\n\n"
        "CRITICAL COMMENT REQUIREMENTS:\n"
        "- STRICT CHARACTER LIMIT: Comments must be between 150-200 characters (including spaces and punctuation)\n"
        "- SENTENCE LIMIT: Use 1-3 sentences maximum\n"
        "- CONTEXT-AWARE ENDINGS: Vary between statements, questions, and calls-to-action based on post content:\n"
        "  * Use statements for sharing insights or agreement (e.g., 'This aligns with industry trends I've observed...')\n"
        "  * Use questions for encouraging discussion (e.g., 'How has this approach worked in your experience?')\n"
        "  * Use calls-to-action for networking or collaboration (e.g., 'Would love to hear more perspectives on this.')\n"
        "- GENUINE CONTENT ALIGNMENT: Comments must be specific to the post content, not generic templates\n"
        "- NATURAL HUMAN TONE: Use simple vocabulary, contractions, and conversational language\n"
        "- AVOID GENERIC STARTERS: Never start with 'Totally agree!', 'Great post!', 'Thanks for sharing!', or similar generic phrases\n"
        "- BE SPECIFIC: Reference specific details, concepts, or insights from the post content\n"
        "- ABSOLUTELY NO HASHTAGS: Never include any hashtags (#) in comments - this is strictly forbidden\n"
        "- DEMONSTRATE UNDERSTANDING: Show that you've read and understood the specific post content\n"
        "- CONTEXTUAL RELEVANCE: Your comment must directly relate to the main topic, key points, or specific details mentioned in the post\n\n"
        "VOCABULARY REQUIREMENTS:\n"
        "- Use simple, clear words that everyone can understand\n"
        "- Avoid complex or fancy vocabulary - choose common, everyday words instead\n"
        "- Write at a level that is easy for all professionals to read and understand\n"
        "- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')\n"
        "- Focus on clarity and understanding over sounding sophisticated\n"
        "- Make content accessible to professionals of all backgrounds and education levels\n\n"
        "Comment Guidelines:\n"
        "- MUST be 150-200 characters exactly (count carefully)\n"
        "- Avoid generic openers like 'Great post!' or 'Thanks for sharing!'\n"
        "- Add specific value related to the post content\n"
        "- Use natural, conversational language with simple words\n"
        "- Choose ending type based on post content and intent\n"
        "- NEVER include hashtags (#) - comments should be conversational, not tagged\n"
        "- Reference specific technologies, concepts, or details mentioned in the post\n"
        "- Show genuine engagement with the post's main message or key points\n"
        "- Focus purely on the content itself, not on any specific professional background\n"
        "- Write from a general professional perspective that could apply to anyone\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{\n  \"suggested_comment\": \"Your suggested comment here\"\n}"
    )
    
    # Generate comment with retry logic for character length validation
    max_retries = 3
    for attempt in range(max_retries):
        temperature = random.uniform(0.8, 1.2)
        response = model.generate_content(prompt, use_cache=False, temperature=temperature)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        try:
            result = json.loads(response_text)
            comment = result.get("suggested_comment", "")

            # Post-processing: Remove hashtags and clean up
            comment = _clean_comment_content(comment)

            # Validate character length (150-200 characters)
            if 150 <= len(comment) <= 200:
                return {"comment": comment}
            elif attempt < max_retries - 1:
                # Add length feedback to prompt for next attempt
                current_length = len(comment)
                if current_length < 150:
                    prompt += f"\n\nPREVIOUS ATTEMPT TOO SHORT ({current_length} chars). Make it longer to reach 150-200 characters."
                else:
                    prompt += f"\n\nPREVIOUS ATTEMPT TOO LONG ({current_length} chars). Make it shorter to stay within 150-200 characters."
                continue
        
        except json.JSONDecodeError:
            if attempt < max_retries - 1:
                continue
    
    # Fallback with QA-compliant comment if all retries fail - purely content-based
    # Extract key topic from post content for fallback
    post_words = post_content.lower().split()
    key_topics = [word for word in post_words if len(word) > 4 and word not in ['about', 'would', 'could', 'should', 'their', 'there', 'where', 'these', 'those']]
    topic = key_topics[0] if key_topics else "this topic"

    fallback_comment = f"Interesting insights on {topic}. This aligns with trends I've seen in the industry. How do you see this evolving?"
    if len(fallback_comment) > 200:
        fallback_comment = f"Great perspective on {topic}. This matches what I've observed. What's your take on the next steps?"
    if len(fallback_comment) < 150:
        fallback_comment = f"Really valuable insights on {topic}. This aligns perfectly with trends I've been seeing in the industry. How do you think this will evolve?"

    return {"comment": fallback_comment}
