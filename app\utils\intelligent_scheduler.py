"""
Intelligent Scheduling Module for LinkedIn Posts

This module provides AI-powered, region-aware scheduling functionality that:
1. Analyzes country input to determine geographic region
2. Uses AI to determine optimal posting times based on region and content category
3. Provides fallback to hardcoded scheduling when country is not provided
"""

import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple
from app.utils.model_initializer import model

# Set up logging
logger = logging.getLogger(__name__)

# Fallback hardcoded scheduling (original logic)
FALLBACK_HOUR_RANGE = (8, 18)
FALLBACK_MINUTES = [0, 15, 30, 45]

def get_timezone_offset(region_analysis: Dict[str, str]) -> str:
    """
    Extract UTC offset from AI-provided region analysis.

    Args:
        region_analysis: Dictionary containing AI analysis with 'utc_offset' field

    Returns:
        ISO 8601 timezone offset string (e.g., '+05:30', '-05:00')
    """
    # Get UTC offset directly from AI analysis
    utc_offset = region_analysis.get('utc_offset', '+00:00')

    # Validate format and return (AI should provide correct format, but validate as fallback)
    if isinstance(utc_offset, str) and len(utc_offset) == 6 and utc_offset[0] in ['+', '-']:
        return utc_offset

    # Fallback to UTC if invalid format
    logger.warning(f"Invalid UTC offset format from AI: {utc_offset}, falling back to UTC")
    return '+00:00'

def analyze_country_and_region(country: str) -> Dict[str, str]:
    """
    Use AI to analyze the country and determine its geographic region and timezone characteristics.
    
    Args:
        country: Country name or ISO code
        
    Returns:
        Dictionary containing region, timezone_info, and analysis
    """
    try:
        prompt = f"""
        Analyze the country "{country}" and provide the following information in JSON format.

        The country input may be in various formats (full name, abbreviation, different cases):
        - Full names: "Pakistan", "United States", "United Kingdom"
        - Abbreviations: "PAK", "USA", "UK", "UAE"
        - Different cases: "pakistan", "PAKISTAN", "PaKiStAn"

        Normalize and identify the country, then provide:

        {{
            "region": "Geographic region (e.g., North America, Europe, Asia-Pacific, Middle East, Africa, South America)",
            "primary_timezone": "Primary timezone name (e.g., EST, GMT, JST, PKT, etc.)",
            "utc_offset": "UTC offset in ±HH:MM format (e.g., +05:00, -05:00, +05:30)",
            "business_hours_local": "Typical business hours in 24h format (e.g., 09:00-17:00)",
            "optimal_linkedin_hours": "Best LinkedIn engagement hours in 24h format (e.g., 08:00-10:00, 17:00-19:00)",
            "country_normalized": "Standardized full country name",
            "cultural_notes": "Brief note about professional social media usage patterns"
        }}

        Important:
        - Recognize country regardless of input format (full name, abbreviation, case)
        - Provide accurate UTC offset for the country's primary timezone
        - Consider current standard time (not daylight saving variations)
        - Focus on the main business timezone if country spans multiple zones

        Return ONLY the JSON object, no additional text.
        """
        
        response = model.generate_content(prompt, use_cache=True)
        response_text = response.text.strip()
        
        # Clean up response to extract JSON
        if "```json" in response_text:
            parts = response_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    response_text = sub_parts[0].strip()
        
        import json
        region_analysis = json.loads(response_text)

        logger.info(f"Region analysis for {country}: {region_analysis}")

        # Debug: Check if utc_offset is present
        if 'utc_offset' not in region_analysis:
            logger.warning(f"AI response missing 'utc_offset' field for {country}. Raw response: {response_text}")
            # Add fallback UTC offset based on timezone
            fallback_mapping = _get_fallback_region_mapping(country)
            region_analysis['utc_offset'] = fallback_mapping.get('utc_offset', '+00:00')
            logger.info(f"Added fallback UTC offset: {region_analysis['utc_offset']}")

        return region_analysis
        
    except Exception as e:
        logger.error(f"Error analyzing country {country}: {str(e)}")
        # Fallback to basic region mapping
        return _get_fallback_region_mapping(country)

def _get_fallback_region_mapping(country: str) -> Dict[str, str]:
    """
    Fallback region mapping when AI analysis fails.
    """
    country_lower = country.lower()

    # Basic region mapping with UTC offsets
    if any(c in country_lower for c in ['usa', 'united states', 'us', 'america']):
        region = "North America"
        timezone = "EST"
        utc_offset = "-05:00"
    elif any(c in country_lower for c in ['canada', 'can']):
        region = "North America"
        timezone = "EST"
        utc_offset = "-05:00"
    elif any(c in country_lower for c in ['uk', 'united kingdom', 'britain', 'england']):
        region = "Europe"
        timezone = "GMT"
        utc_offset = "+00:00"
    elif any(c in country_lower for c in ['germany', 'france', 'italy', 'spain', 'netherlands']):
        region = "Europe"
        timezone = "CET"
        utc_offset = "+01:00"
    elif any(c in country_lower for c in ['india', 'ind']):
        region = "Asia-Pacific"
        timezone = "IST"
        utc_offset = "+05:30"
    elif any(c in country_lower for c in ['china', 'chn']):
        region = "Asia-Pacific"
        timezone = "CST"
        utc_offset = "+08:00"
    elif any(c in country_lower for c in ['japan', 'jpn']):
        region = "Asia-Pacific"
        timezone = "JST"
        utc_offset = "+09:00"
    elif any(c in country_lower for c in ['australia', 'aus']):
        region = "Asia-Pacific"
        timezone = "AEST"
        utc_offset = "+10:00"
    elif any(c in country_lower for c in ['pakistan', 'pak']):
        region = "Asia-Pacific"
        timezone = "PKT"
        utc_offset = "+05:00"
    else:
        region = "Global"
        timezone = "UTC"
        utc_offset = "+00:00"

    return {
        "region": region,
        "primary_timezone": timezone,
        "utc_offset": utc_offset,
        "business_hours_local": "09:00-17:00",
        "optimal_linkedin_hours": "08:00-10:00, 17:00-19:00",
        "country_normalized": country,
        "cultural_notes": "Standard professional networking patterns"
    }

def determine_optimal_posting_time(category: str, region_analysis: Dict[str, str], day_index: int) -> Dict[str, any]:
    """
    Use AI to determine the optimal posting time based on category and region.
    
    Args:
        category: Content category (e.g., "Career Advice", "Tech Trends", etc.)
        region_analysis: Region analysis from analyze_country_and_region()
        day_index: Day index for variety in scheduling
        
    Returns:
        Dictionary containing hour, minute, and reasoning
    """
    try:
        prompt = f"""
        Determine the optimal LinkedIn posting time for the following scenario:
        
        **Content Category**: {category}
        **Region**: {region_analysis.get('region', 'Global')}
        **Primary Timezone**: {region_analysis.get('primary_timezone', 'UTC')}
        **Optimal LinkedIn Hours**: {region_analysis.get('optimal_linkedin_hours', '08:00-10:00, 17:00-19:00')}
        **Cultural Notes**: {region_analysis.get('cultural_notes', 'Standard patterns')}
        **Day Index**: {day_index} (for scheduling variety)
        
        Consider:
        1. When professionals in this region are most active on LinkedIn
        2. The type of content and when it gets best engagement
        3. Avoiding oversaturation by varying times across the schedule
        4. Regional work patterns and commute times
        
        Provide your recommendation in JSON format:
        {{
            "hour": "Hour in 24h format (0-23)",
            "minute": "Minute (0, 15, 30, or 45)",
            "reasoning": "Brief explanation of why this time is optimal for this category and region",
            "engagement_expectation": "Expected engagement level (High/Medium/Low)"
        }}
        
        Return ONLY the JSON object.
        """
        
        response = model.generate_content(prompt, use_cache=False)  # Don't cache for variety
        response_text = response.text.strip()
        
        # Clean up response to extract JSON
        if "```json" in response_text:
            parts = response_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    response_text = sub_parts[0].strip()
        
        import json
        timing_analysis = json.loads(response_text)
        
        # Validate and sanitize the response
        hour = int(timing_analysis.get("hour", 9))
        minute = int(timing_analysis.get("minute", 0))
        
        # Ensure hour is in valid range
        hour = max(0, min(23, hour))
        
        # Ensure minute is one of the allowed values
        if minute not in [0, 15, 30, 45]:
            minute = min([0, 15, 30, 45], key=lambda x: abs(x - minute))
        
        result = {
            "hour": hour,
            "minute": minute,
            "reasoning": timing_analysis.get("reasoning", "AI-optimized timing for maximum engagement"),
            "engagement_expectation": timing_analysis.get("engagement_expectation", "Medium")
        }
        
        logger.info(f"Optimal timing for {category} in {region_analysis.get('region')}: {hour:02d}:{minute:02d} - {result['reasoning']}")
        return result
        
    except Exception as e:
        logger.error(f"Error determining optimal posting time: {str(e)}")
        # Fallback to randomized time within business hours
        return {
            "hour": random.randint(FALLBACK_HOUR_RANGE[0], FALLBACK_HOUR_RANGE[1]),
            "minute": random.choice(FALLBACK_MINUTES),
            "reasoning": "Fallback timing due to AI analysis error",
            "engagement_expectation": "Medium"
        }

def get_intelligent_posting_time(post_date: datetime, category: str, country: Optional[str] = None, day_index: int = 0) -> Tuple[datetime, Dict[str, any]]:
    """
    Main function to get intelligent posting time with optional country-based optimization.
    
    Args:
        post_date: Base date for the post
        category: Content category
        country: Optional country for region-aware scheduling
        day_index: Day index for variety
        
    Returns:
        Tuple of (scheduled_datetime, scheduling_metadata)
    """
    if country:
        # AI-powered intelligent scheduling
        logger.info(f"Using intelligent scheduling for country: {country}, category: {category}")
        
        # Step 1: Analyze country and region
        region_analysis = analyze_country_and_region(country)
        
        # Step 2: Determine optimal posting time
        timing_result = determine_optimal_posting_time(category, region_analysis, day_index)
        
        # Step 3: Create scheduled datetime with timezone
        timezone_str = region_analysis.get("primary_timezone", "UTC")
        timezone_offset = get_timezone_offset(region_analysis)

        # Parse timezone offset to create timezone object
        sign = 1 if timezone_offset[0] == '+' else -1
        hours, minutes = map(int, timezone_offset[1:].split(':'))
        tz_offset = timezone(timedelta(hours=sign * hours, minutes=sign * minutes))

        scheduled_datetime = post_date.replace(
            hour=timing_result["hour"],
            minute=timing_result["minute"],
            second=0,
            microsecond=0,
            tzinfo=tz_offset
        )

        # Step 4: Create metadata
        metadata = {
            "scheduling_method": "intelligent",
            "country": country,
            "region": region_analysis.get("region"),
            "timezone": timezone_str,
            "timezone_offset": timezone_offset,
            "reasoning": timing_result["reasoning"],
            "engagement_expectation": timing_result["engagement_expectation"]
        }
        
    else:
        # Fallback to original hardcoded scheduling (use UTC timezone)
        logger.info(f"Using fallback scheduling for category: {category}")

        # Use UTC timezone for fallback scheduling
        utc_timezone = timezone.utc

        scheduled_datetime = post_date.replace(
            hour=random.randint(FALLBACK_HOUR_RANGE[0], FALLBACK_HOUR_RANGE[1]),
            minute=random.choice(FALLBACK_MINUTES),
            second=0,
            microsecond=0,
            tzinfo=utc_timezone
        )

        metadata = {
            "scheduling_method": "fallback",
            "country": None,
            "region": None,
            "timezone": "UTC",
            "timezone_offset": "+00:00",
            "reasoning": "Standard business hours scheduling",
            "engagement_expectation": "Medium"
        }
    
    return scheduled_datetime, metadata
